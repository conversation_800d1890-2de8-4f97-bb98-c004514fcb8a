import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth/auth.service';
import { SubdomainService } from 'src/app/core/services/subdomain/subdomain.service';

@Component({
  selector: 'app-user-layout',
  templateUrl: './user-layout.component.html',
  styleUrls: ['./user-layout.component.css'],
})
export class UserLayoutComponent {
  userName: string = '';

  constructor(
    private authService: AuthService,
    private router: Router,
    private subdomainService: SubdomainService
  ) {}

  ngOnInit(): void {
    const user = this.authService.getCurrentUser();
    if (user) {
      this.userName = user.name;
    }
  }

  logout(): void {
    console.log('UserLayout: Logging out user');

    // If on subdomain, use cross-domain logout to clear tokens from both domains
    if (this.subdomainService.areSubdomainsEnabled()) {
      const currentSubdomain = this.subdomainService.getCurrentSubdomain();
      if (currentSubdomain) {
        console.log('UserLayout: On subdomain, initiating cross-domain logout');
        // Use the new cross-domain logout method
        this.authService.logoutFromSubdomain();
        this.authService.logoutFromSubdomain();
        return; // Don't navigate here, the logout will redirect to main domain
      }
    }

    // Fallback for main domain logout
    console.log('UserLayout: On main domain, using regular logout');

    // Call logout twice for reliability
    this.authService.logout();
    this.authService.logout();

    // Navigate to login only after logout calls
    setTimeout(() => {
      this.router.navigate(['/auth/login']);
    }, 100);
  }
}
